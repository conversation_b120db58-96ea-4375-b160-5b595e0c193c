% Generated by IEEEtran.bst, version: 1.14 (2015/08/26)
\begin{thebibliography}{10}
\providecommand{\url}[1]{#1}
\csname url@samestyle\endcsname
\providecommand{\newblock}{\relax}
\providecommand{\bibinfo}[2]{#2}
\providecommand{\BIBentrySTDinterwordspacing}{\spaceskip=0pt\relax}
\providecommand{\BIBentryALTinterwordstretchfactor}{4}
\providecommand{\BIBentryALTinterwordspacing}{\spaceskip=\fontdimen2\font plus
\BIBentryALTinterwordstretchfactor\fontdimen3\font minus \fontdimen4\font\relax}
\providecommand{\BIBforeignlanguage}[2]{{%
\expandafter\ifx\csname l@#1\endcsname\relax
\typeout{** WARNING: IEEEtran.bst: No hyphenation pattern has been}%
\typeout{** loaded for the language `#1'. Using the pattern for}%
\typeout{** the default language instead.}%
\else
\language=\csname l@#1\endcsname
\fi
#2}}
\providecommand{\BIBdecl}{\relax}
\BIBdecl

\bibitem{Tophat}
X.~<PERSON> and <PERSON><PERSON><PERSON>, ``Analysis of new top-hat transformation and the application for infrared dim small target detection,'' \emph{Pattern Recognition}, vol.~43, no.~6, pp. 2145--2156, 2010.

\bibitem{MaxMeandeshpande1999max}
S.~D. Deshpande, M.~H. Er, R.~Venkateswarlu, and P.~Chan, ``Max-mean and max-median filters for detection of small targets,'' in \emph{Signal and Data Processing of Small Targets 1999}, vol. 3809.\hskip 1em plus 0.5em minus 0.4em\relax SPIE, 1999, pp. 74--83.

\bibitem{chen2013lcm}
C.~P. Chen, H.~Li, Y.~Wei, T.~Xia, and Y.~Y. Tang, ``A local contrast method for small infrared target detection,'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~52, no.~1, pp. 574--581, 2013.

\bibitem{han2018infraredrlcm}
J.~Han, K.~Liang, B.~Zhou, X.~Zhu, J.~Zhao, and L.~Zhao, ``Infrared small target detection utilizing the multiscale relative local contrast measure,'' \emph{IEEE Geoscience and Remote Sensing Letters}, vol.~15, no.~4, pp. 612--616, 2018.

\bibitem{han2019hbmlcm}
J.~Han, S.~Moradi, I.~Faramarzi, C.~Liu, H.~Zhang, and Q.~Zhao, ``A local contrast method for infrared small-target detection utilizing a tri-layer window,'' \emph{IEEE Geoscience and Remote Sensing Letters}, vol.~17, no.~10, pp. 1822--1826, 2019.

\bibitem{han2020wslcm}
J.~Han, S.~Moradi, I.~Faramarzi, H.~Zhang, Q.~Zhao, X.~Zhang, and N.~Li, ``Infrared small target detection based on the weighted strengthened local contrast measure,'' \emph{IEEE Geoscience and Remote Sensing Letters}, vol.~18, no.~9, pp. 1670--1674, 2020.

\bibitem{gao2013infraredIPI}
C.~Gao, D.~Meng, Y.~Yang, Y.~Wang, X.~Zhou, and A.~G. Hauptmann, ``Infrared patch-image model for small target detection in a single image,'' \emph{IEEE Transactions on Image Processing}, vol.~22, no.~12, pp. 4996--5009, 2013.

\bibitem{wang2017SMSL}
X.~Wang, Z.~Peng, D.~Kong, and Y.~He, ``Infrared dim and small target detection based on stable multisubspace learning in heterogeneous scene,'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~55, no.~10, pp. 5481--5493, 2017.

\bibitem{NUAAACM}
Y.~Dai, Y.~Wu, F.~Zhou, and K.~Barnard, ``{Asymmetric Contextual Modulation for Infrared Small Target Detection},'' in \emph{2021 IEEE Winter Conference on Applications of Computer Vision (WACV)}, 2021, pp. 949--958.

\bibitem{ISNetzhang2022isnet}
M.~Zhang, R.~Zhang, Y.~Yang, H.~Bai, J.~Zhang, and J.~Guo, ``{ISNet}: Shape matters for infrared small target detection,'' in \emph{Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition}, 2022, pp. 877--886.

\bibitem{MDFAwang2019miss}
H.~Wang, L.~Zhou, and L.~Wang, ``Miss detection vs. false alarm: Adversarial learning for small object segmentation in infrared images,'' in \emph{Proceedings of the IEEE/CVF International Conference on Computer Vision}, 2019, pp. 8509--8518.

\bibitem{DNAnetli2022dense}
B.~Li, C.~Xiao, L.~Wang, Y.~Wang, Z.~Lin, M.~Li, W.~An, and Y.~Guo, ``Dense nested attention network for infrared small target detection,'' \emph{IEEE Transactions on Image Processing}, vol.~32, pp. 1745--1758, 2022.

\bibitem{dai2021ALCNet}
Y.~Dai, Y.~Wu, F.~Zhou, and K.~Barnard, ``Attentional local contrast networks for infrared small target detection,'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~59, no.~11, pp. 9813--9824, 2021.

\bibitem{AGPCNet}
T.~Zhang, L.~Li, S.~Cao, T.~Pu, and Z.~Peng, ``{Attention-Guided Pyramid Context Networks for Detecting Infrared Small Target Under Complex Background},'' \emph{IEEE Transactions on Aerospace and Electronic Systems}, vol.~59, no.~4, pp. 4250--4261, 2023.

\bibitem{RDIAN}
H.~Sun, J.~Bai, F.~Yang, and X.~Bai, ``{Receptive-Field and Direction Induced Attention Network for Infrared Dim Small Target Detection With a Large-Scale Dataset IRDST},'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~61, pp. 1--13, 2023.

\bibitem{IRTransDet}
J.~Lin, S.~Li, L.~Zhang, X.~Yang, B.~Yan, and Z.~Meng, ``{IR-TransDet: Infrared Dim and Small Target Detection With IR-Transformer},'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~61, pp. 1--13, 2023.

\bibitem{wu2022uiuNet}
X.~Wu, D.~Hong, and J.~Chanussot, ``{UIU-Net: U-Net in U-Net for infrared small object detection},'' \emph{IEEE Transactions on Image Processing}, vol.~32, pp. 364--376, 2022.

\bibitem{yang2024eflnet}
B.~Yang, X.~Zhang, J.~Zhang, J.~Luo, M.~Zhou, and Y.~Pi, ``{EFLNet: Enhancing Feature Learning Network for Infrared Small Target Detection},'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~62, pp. 1--11, 2024.

\bibitem{wu2024rpcanet}
F.~Wu, T.~Zhang, L.~Li, Y.~Huang, and Z.~Peng, ``{RPCANet: Deep Unfolding RPCA Based Infrared Small Target Detection},'' in \emph{Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision}, 2024, pp. 4809--4818.

\bibitem{kwan2020optical1}
C.~Kwan and B.~Budavari, ``Enhancing small moving target detection performance in low-quality and long-range infrared videos using optical flow techniques,'' \emph{Remote Sensing}, vol.~12, no.~24, p. 4024, 2020.

\bibitem{optical}
F.~Zhao, T.~Wang, S.~Shao, E.~Zhang, and G.~Lin, ``{Infrared Moving Small-Target Detection via Spatiotemporal Consistency of Trajectory Points},'' \emph{IEEE Geoscience and Remote Sensing Letters}, vol.~17, no.~1, pp. 122--126, 2020.

\bibitem{wu20234Dtensor}
F.~Wu, H.~Yu, A.~Liu, J.~Luo, and Z.~Peng, ``{Infrared Small Target Detection Using Spatiotemporal 4-D Tensor Train and Ring Unfolding},'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, 2023.

\bibitem{luo2024feedback}
Y.~Luo, X.~Li, and S.~Chen, ``{Feedback Spatial-Temporal Infrared Small Target Detection based on Orthogonal Subspace Projection},'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, 2024.

\bibitem{wang2021NPSTT}
G.~Wang, B.~Tao, X.~Kong, and Z.~Peng, ``Infrared small target detection using nonoverlapping patch spatial-temporal tensor factorization with capped nuclear norm regularization,'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~60, pp. 1--17, 2021.

\bibitem{liu2021nonconvex}
T.~Liu, J.~Yang, B.~Li, C.~Xiao, Y.~Sun, Y.~Wang, and W.~An, ``Nonconvex tensor low-rank approximation for infrared small target detection,'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~60, pp. 1--18, 2021.

\bibitem{zhang2005enery}
F.~Zhang, C.~Li, and L.~Shi, ``{Detecting and tracking dim moving point target in IR image sequence},'' \emph{Infrared Physics \& Technology}, vol.~46, no.~4, pp. 323--328, 2005.

\bibitem{ren2019energy2}
X.~Ren, J.~Wang, T.~Ma, K.~Bai, M.~Ge, and Y.~Wang, ``Infrared dim and small target detection based on three-dimensional collaborative filtering and spatial inversion modeling,'' \emph{Infrared physics \& technology}, vol. 101, pp. 13--24, 2019.

\bibitem{sun2017dp}
X.~Sun, X.~Liu, Z.~Tang, G.~Long, and Q.~Yu, ``Real-time visual enhancement for infrared small dim targets in video,'' \emph{Infrared physics \& technology}, vol.~83, pp. 217--226, 2017.

\bibitem{du2021spatial}
J.~Du, H.~Lu, L.~Zhang, M.~Hu, S.~Chen, Y.~Deng, X.~Shen, and Y.~Zhang, ``A spatial-temporal feature-based detection framework for infrared dim small target,'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~60, pp. 1--12, 2021.

\bibitem{yan2023stdmanet}
P.~Yan, R.~Hou, X.~Duan, C.~Yue, X.~Wang, and X.~Cao, ``{STDMANet: Spatio-temporal differential multiscale attention network for small moving infrared target detection},'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~61, pp. 1--16, 2023.

\bibitem{li2023directiondtum}
R.~Li, W.~An, C.~Xiao, B.~Li, Y.~Wang, M.~Li, and Y.~Guo, ``{Direction-coded temporal U-shape module for multiframe infrared small target detection},'' \emph{IEEE Transactions on Neural Networks and Learning Systems}, 2023.

\bibitem{chen2024sstnet}
S.~Chen, L.~Ji, J.~Zhu, M.~Ye, and X.~Yao, ``{SSTNet: Sliced Spatio-Temporal Network With Cross-Slice ConvLSTM for Moving Infrared Dim-Small Target Detection},'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~62, pp. 1--12, 2024.

\bibitem{tong2024st-trans}
X.~Tong, Z.~Zuo, S.~Su, J.~Wei, X.~Sun, P.~Wu, and Z.~Zhao, ``{ST-Trans: Spatial-Temporal Transformer for Infrared Small Target Detection in Sequential Images},'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, 2024.

\bibitem{ITSDT}
R.~Fu, H.~Fan, Y.~Zhu, B.~Hui, Z.~Zhang, P.~Zhong, D.~Li, S.~Zhang, G.~Chen, and L.~Wang, ``{A dataset for infrared time-sensitive target detection and tracking for air-ground application},'' May 2022.

\bibitem{ge2021yolox}
Z.~Ge, S.~Liu, F.~Wang, Z.~Li, and J.~Sun, ``{YOLOX: Exceeding yolo series in 2021},'' \emph{arXiv preprint arXiv:2107.08430}, 2021.

\bibitem{lin2014COCO}
T.-Y. Lin, M.~Maire, S.~Belongie, J.~Hays, P.~Perona, D.~Ramanan, P.~Doll{\'a}r, and C.~L. Zitnick, ``Microsoft coco: Common objects in context,'' in \emph{Computer Vision--ECCV 2014: 13th European Conference, Zurich, Switzerland, September 6-12, 2014, Proceedings, Part V 13}.\hskip 1em plus 0.5em minus 0.4em\relax Springer, 2014, pp. 740--755.

\bibitem{1982fft}
J.~W. Cooley, P.~A. Lewis, and P.~D. Welch, ``{The fast Fourier transform and its applications},'' \emph{IEEE Transactions on Education}, vol.~12, no.~1, pp. 27--34, 1969.

\bibitem{2022videoswin}
Z.~Liu, J.~Ning, Y.~Cao, Y.~Wei, Z.~Zhang, S.~Lin, and H.~Hu, ``{Video Swin Transformer},'' in \emph{Proceedings of the IEEE/CVF conference on computer vision and pattern recognition}, 2022, pp. 3202--3211.

\bibitem{lin2017focalloss}
T.-Y. Lin, P.~Goyal, R.~Girshick, K.~He, and P.~Doll{\'a}r, ``Focal loss for dense object detection,'' in \emph{Proceedings of the IEEE International Conference on Computer Vision}, 2017, pp. 2980--2988.

\bibitem{xu2022nwd}
C.~Xu, J.~Wang, W.~Yang, H.~Yu, L.~Yu, and G.-S. Xia, ``Detecting tiny objects in aerial images: A normalized wasserstein distance and a new benchmark,'' \emph{ISPRS Journal of Photogrammetry and Remote Sensing}, vol. 190, pp. 79--93, 2022.

\bibitem{zheng2021Ciou}
Z.~Zheng, P.~Wang, D.~Ren, W.~Liu, R.~Ye, Q.~Hu, and W.~Zuo, ``Enhancing geometric factors in model learning and inference for object detection and instance segmentation,'' \emph{IEEE Transactions on Cybernetics}, vol.~52, no.~8, pp. 8574--8586, 2021.

\bibitem{DAUB}
B.~Hui, Z.~Song, H.~Fan, P.~Zhong, W.~Hu, X.~Zhang, J.~Lin, H.~Su, W.~Jin, Y.~Zhang, and Y.~Bai, ``{A dataset for infrared image dim-small aircraft target detection and tracking under ground / air background},'' Oct. 2019.

\bibitem{hou2021ristdnet}
Q.~Hou, Z.~Wang, F.~Tan, Y.~Zhao, H.~Zheng, and W.~Zhang, ``{RISTDnet: Robust infrared small target detection network},'' \emph{IEEE Geoscience and Remote Sensing Letters}, vol.~19, pp. 1--5, 2021.

\bibitem{zhu2023SAnet}
J.~Zhu, S.~Chen, L.~Li, and L.~Ji, ``{SANet: Spatial attention network with global average contrast learning for infrared small target detection},'' in \emph{ICASSP 2023-2023 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP)}.\hskip 1em plus 0.5em minus 0.4em\relax IEEE, 2023, pp. 1--5.

\bibitem{lu2024sirst5k}
Y.~Lu, Y.~Lin, H.~Wu, X.~Xian, Y.~Shi, and L.~Lin, ``{SIRST-5K: Exploring Massive Negatives Synthesis with Self-supervised Learning for Robust Infrared Small Target Detection},'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, 2024.

\bibitem{liu2024MSH}
Q.~Liu, R.~Liu, B.~Zheng, H.~Wang, and Y.~Fu, ``Infrared small target detection with scale and location sensitivity,'' in \emph{Proceedings of the IEEE/CVF Computer Vision and Pattern Recognition}, 2024.

\bibitem{kou2023survey}
R.~Kou, C.~Wang, Z.~Peng, Z.~Zhao, Y.~Chen, J.~Han, F.~Huang, Y.~Yu, and Q.~Fu, ``{Infrared small target segmentation networks: A survey},'' \emph{Pattern Recognition}, vol. 143, p. 109788, 2023.

\bibitem{zhao2022survey}
M.~Zhao, W.~Li, L.~Li, J.~Hu, P.~Ma, and R.~Tao, ``{Single-frame infrared small-target detection: A survey},'' \emph{IEEE Geoscience and Remote Sensing Magazine}, vol.~10, no.~2, pp. 87--119, 2022.

\bibitem{bai2018derivative}
X.~Bai and Y.~Bi, ``Derivative entropy-based contrast measure for infrared small-target detection,'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~56, no.~4, pp. 2452--2466, 2018.

\bibitem{xu2020frelearn}
K.~Xu, M.~Qin, F.~Sun, Y.~Wang, Y.-K. Chen, and F.~Ren, ``Learning in the frequency domain,'' in \emph{Proceedings of the IEEE/CVF conference on computer vision and pattern recognition}, 2020, pp. 1740--1749.

\bibitem{li2015fre}
J.~Li, L.-Y. Duan, X.~Chen, T.~Huang, and Y.~Tian, ``{Finding the secret of image saliency in the frequency domain},'' \emph{IEEE Transactions on Pattern Analysis and Machine Intelligence}, vol.~37, no.~12, pp. 2428--2440, 2015.

\bibitem{chen2024micpl}
S.~Chen, L.~Ji, S.~Zhu, and M.~Ye, ``{MICPL: Motion-Inspired Cross-Pattern Learning for Small-Object Detection in Satellite Videos},'' \emph{IEEE Transactions on Neural Networks and Learning Systems}, pp. 1--14, 2024.

\bibitem{chen2023augtarget}
S.~Chen, J.~Zhu, L.~Ji, H.~Pan, and Y.~Xu, ``Augtarget data augmentation for infrared small target detection,'' in \emph{ICASSP 2023-2023 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP)}.\hskip 1em plus 0.5em minus 0.4em\relax IEEE, 2023, pp. 1--5.

\bibitem{wang2021tdn}
L.~Wang, Z.~Tong, B.~Ji, and G.~Wu, ``{TDN: Temporal difference networks for efficient action recognition},'' in \emph{Proceedings of the IEEE/CVF conference on computer vision and pattern recognition}, 2021, pp. 1895--1904.

\bibitem{xiao2023stdm}
Y.~Xiao, Q.~Yuan, K.~Jiang, X.~Jin, J.~He, L.~Zhang, and C.-w. Lin, ``{Local-global temporal difference learning for satellite video super-resolution},'' \emph{IEEE Transactions on Circuits and Systems for Video Technology}, 2023.

\bibitem{zhu2024tmp}
S.~Zhu, L.~Ji, J.~Zhu, S.~Chen, and W.~Duan, ``{TMP: Temporal Motion Perception with spatial auxiliary enhancement for moving Infrared dim-small target detection},'' \emph{Expert Systems with Applications}, p. 124731, 2024.

\bibitem{tong2024strans}
X.~Tong, Z.~Zuo, S.~Su, J.~Wei, X.~Sun, P.~Wu, and Z.~Zhao, ``{ST-Trans: Spatial-Temporal Transformer for Infrared Small Target Detection in Sequential Images},'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, 2024.

\bibitem{1145}
K.~Jiang, W.~Liu, Z.~Wang, X.~Zhong, J.~Jiang, and C.-W. Lin, ``{DAWN: Direction-aware Attention Wavelet Network for Image Deraining},'' in \emph{Proceedings of the 31st ACM International Conference on Multimedia}, ser. MM '23.\hskip 1em plus 0.5em minus 0.4em\relax New York, NY, USA: Association for Computing Machinery, 2023, p. 7065–7074.

\bibitem{1609}
K.~Jiang, J.~Jiang, X.~Liu, X.~Xu, and X.~Ma, ``{FMRNet: Image Deraining via Frequency Mutual Revision},'' \emph{Proceedings of the AAAI Conference on Artificial Intelligence}, vol.~38, no.~11, pp. 12\,892--12\,900, Mar. 2024.

\bibitem{4964}
Y.~Xiao, Q.~Yuan, K.~Jiang, Y.~Chen, Q.~Zhang, and C.-W. Lin, ``{Frequency-Assisted Mamba for Remote Sensing Image Super-Resolution},'' 2024.

\bibitem{SpectralGPT}
D.~Hong, B.~Zhang, X.~Li, Y.~Li, C.~Li, J.~Yao, N.~Yokoya, H.~Li, P.~Ghamisi, X.~Jia, A.~Plaza, P.~Gamba, J.~A. Benediktsson, and J.~Chanussot, ``{SpectralGPT: Spectral Remote Sensing Foundation Model},'' \emph{IEEE Transactions on Pattern Analysis and Machine Intelligence}, vol.~46, no.~8, pp. 5227--5244, 2024.

\bibitem{LRRNet}
C.~Li, B.~Zhang, D.~Hong, J.~Yao, and J.~Chanussot, ``{LRR-Net: An Interpretable Deep Unfolding Network for Hyperspectral Anomaly Detection},'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~61, pp. 1--12, 2023.

\bibitem{CasFormer}
C.~Li, B.~Zhang, D.~Hong, J.~Zhou, G.~Vivone, S.~Li, and J.~Chanussot, ``{CasFormer: Cascaded transformers for fusion-aware computational hyperspectral imaging},'' \emph{Information Fusion}, vol. 108, p. 102408, 2024.

\end{thebibliography}
